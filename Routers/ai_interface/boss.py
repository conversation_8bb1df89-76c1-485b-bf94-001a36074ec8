import httpx
import base64
import json
import asyncio
from openai import AsyncOpenAI
from fastapi import Request
from DadabaseControl.DatabaseControl3 import get_apikey_by_value,insert_apikey_token_usage

async def visual_understanding(
    image_type: str,
    image_base64: str, 
    prompt_text: str, 
    request: Request,
    max_tokens: int = 300
) -> dict:
    """
    使用 httpx 异步发送包含图片的 API 请求。

    参数:
        image_type (str): 图片类型
        image_base64 (str): 图片base64。
        prompt_text (str): 你想问的问题文本。
        max_tokens (int): 返回的最大 token 数量，默认为 300。

    返回:
        dict: API 响应的 JSON 数据。
    """
    try:
        apikey = request.headers.get("Authorization").replace("Bearer ","")
        tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    except Exception as e:
        return {"code": 400, "message": "apikey not found"}

    # 构建请求 payload
    payload = {
        "model": "doubao-seed-1-6-250615",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/{image_type};base64,{image_base64}"
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt_text
                    }
                ]
            }
        ],
        "max_tokens": max_tokens
    }

    with open("config.json","r") as f:
        config = json.load(f)
    api_key = config["openai_config"]["api_key"]
    if api_key is None:
        return {"code":400,"messgae":"api_key not found"}

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    api_url = "http://ark.cn-beijing.volces.com/api/v3/chat/completions"

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(api_url, headers=headers, json=payload, timeout=30.0)
            response.raise_for_status()  # 如果请求失败，会抛出异常哦
            result_json = response.json()
            content = result_json["choices"][0]["message"]["content"]
            print(result_json)

            # 记录Token使用量
            if apikey and tenant_id:
                prompt_tokens = result_json.usage.prompt_tokens
                completion_tokens = response.usage.completion_tokens
                insert_apikey_token_usage(tenant_id, apikey, prompt_tokens, completion_tokens)
                print(f"记录Token使用量: prompt={prompt_tokens}, completion={completion_tokens}")

            return {"code":200,"message":content}
        except Exception as e:
            print(f"发生了一个错误：{e}")
            return {"code": 500,"message": str(e)}
        


async def text_reasoning(prompt: str,messages,request:Request):
    try:
        apikey = request.headers.get("Authorization").replace("Bearer ","")
        tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    except Exception as e:
        return {"code": 400, "message": "apikey not found"}
    print("messages:", messages)
    with open("config.json", "r") as f:
        config = json.load(f)
    openai_config = config.get("openai_config")
    openai_client = AsyncOpenAI(base_url=openai_config.get("base_url"), api_key=openai_config.get("api_key"))
    
    response = await openai_client.chat.completions.create(
        model=openai_config.get("model_name"),
        messages=[{"role": "system", "content": prompt}] + messages,
    )
    
    # 记录Token使用量
    if apikey and tenant_id:
        prompt_tokens = response.usage.prompt_tokens
        completion_tokens = response.usage.completion_tokens
        insert_apikey_token_usage(tenant_id, apikey, prompt_tokens, completion_tokens)
        print(f"记录Token使用量: prompt={prompt_tokens}, completion={completion_tokens}")
    
    return response.choices[0].message.content



with open("/Users/<USER>/Desktop/111.png", "rb") as image_file:
    base64_image = base64.b64encode(image_file.read()).decode("utf-8")

asyncio.run(visual_understanding(
    image_type="png",
    image_base64=base64_image,
    prompt_text="请描述一下这个图片的内容"
))