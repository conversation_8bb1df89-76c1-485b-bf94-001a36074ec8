/* 面试互动页面样式 */
.interview-container {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);
}

.users-panel,
.chat-panel {
    background-color: white;
    border-radius: 5px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.users-panel {
    flex: 1;
    max-width: 300px;
    display: flex;
    flex-direction: column;
}

.chat-panel {
    flex: 3;
    display: flex;
    flex-direction: column;
}

.panel-header {
    padding: 15px;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.users-panel .panel-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding-bottom: 12px;
}

.users-panel .panel-header h3 {
    font-weight: 600;
    margin: 0 0 10px 0;
    font-size: 16px;
    color: var(--secondary-color);
}

.chat-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.panel-body {
    padding: 0;
    overflow-y: auto;
    flex: 1;
}

/* 搜索框 */
.search-box {
    position: relative;
    width: 100%;
    display: flex;
}

.search-box input {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 14px;
}

.search-box i {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

/* 用户列表样式 */
.users-list {
    height: 100%;
}

/* 租户和职位分类分组样式 */
.tenant-group, .classification-group {
    border-bottom: 1px solid var(--border-color);
}

.tenant-group:last-child, .classification-group:last-child {
    border-bottom: none;
}

.group-header {
    padding: 10px 15px;
    background-color: var(--bg-light);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s;
}

.group-header:hover {
    background-color: rgba(0,0,0,0.03);
}

.group-header i {
    margin-right: 8px;
    color: var(--secondary-color);
}

.group-header .toggle-icon {
    margin-right: 0;
    margin-left: auto;
    font-size: 12px;
    color: var(--text-light);
}

.tenant-header {
    background-color: rgba(0,0,0,0.02);
}

.classification-header {
    padding-left: 25px;
    font-size: 14px;
}

.classification-count {
    font-size: 12px;
    color: var(--text-light);
    margin-left: 5px;
}

.tenant-content, .classification-content {
    transition: all 0.3s;
}

.tenant-group.collapsed .tenant-content,
.classification-group.collapsed .classification-content {
    display: none;
}

.user-item {
    padding: 12px 15px 12px 35px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.2s;
}

.user-item:hover {
    background-color: var(--bg-light);
}

.user-item.active {
    background-color: var(--primary-color);
    color: white;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    flex: 1;
    overflow: hidden;
}

.user-name {
    font-weight: 500;
    margin: 0 0 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-position {
    font-size: 12px;
    color: var(--text-light);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-item.active .user-position {
    color: rgba(255, 255, 255, 0.8);
}

.users-list-loading {
    padding: 20px;
    text-align: center;
    color: var(--text-light);
}

.loading-message, .error-message, .no-users-message {
    padding: 15px;
    text-align: center;
    font-size: 14px;
}

.loading-message {
    color: var(--text-light);
    font-style: italic;
}

.error-message {
    color: var(--danger-color, #e74c3c);
    background-color: rgba(231, 76, 60, 0.1);
    border-radius: 4px;
}

.no-users-message {
    color: var(--text-light);
    background-color: var(--bg-light);
    border-radius: 4px;
}

/* 聊天窗口样式 */
.chat-user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

.chat-user-details {
    display: flex;
    flex-direction: column;
}

.chat-user-details h3 {
    margin: 0;
    font-size: 16px;
}

.chat-user-details p {
    margin: 0;
    font-size: 12px;
    color: var(--text-light);
}

.chat-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-score {
    font-size: 14px;
    color: var(--text-color);
    padding: 5px 10px;
    background-color: var(--bg-light);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.chat-scene {
    font-size: 14px;
    color: var(--text-color);
    padding: 5px 10px;
    background-color: var(--bg-light);
    border-radius: 4px;
    border: 1px solid var(--border-color);
    margin-right: 10px;
}

.chat-scene span {
    font-weight: 600;
    color: var(--secondary-color);
}

.chat-score span {
    font-weight: 600;
    color: var(--primary-color);
}

.chat-window {
    height: 100%;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.no-user-selected {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-light);
}

.no-user-content {
    text-align: center;
}

.no-user-content i {
    font-size: 40px;
    margin-bottom: 10px;
    color: var(--primary-color);
    opacity: 0.5;
}

.chat-input-area {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.chat-input-area textarea {
    flex: 1;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 14px;
    resize: none;
    min-height: 50px;
    max-height: 150px;
    overflow-y: auto;
    line-height: 1.5;
    font-family: inherit;
}

.chat-input-area textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.chat-input-actions {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.chat-input-hint {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 5px;
    text-align: right;
}

/* 消息结构样式 */
.message-container {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.message {
    padding: 12px 15px;
    border-radius: 5px;
    max-width: 80%;
    position: relative;
    word-break: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-user {
    background-color: var(--primary-light);
    color: var(--text-color);
    align-self: flex-end;
}

.message-system {
    background-color: #f5f5f5;
    color: var(--text-color);
    align-self: flex-start;
}

.message-content {
    position: relative;
    z-index: 1;
}

/* Channel 标签样式 */
.message.has-channel {
    padding-top: 24px; /* 为channel标签腾出空间 */
}

.channel-tag {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 0 5px 0 5px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    opacity: 0.8;
    background-color: rgba(0, 0, 0, 0.15);
    color: rgba(0, 0, 0, 0.7);
}

.message-user .channel-tag {
    background-color: rgba(0, 0, 0, 0.15);
    color: rgba(0, 0, 0, 0.7);
}

.message-system .channel-tag {
    background-color: rgba(0, 0, 0, 0.15);
    color: rgba(0, 0, 0, 0.7);
}

/* 特定channel样式 */
.message-user .channel-tag[data-channel="wechat"],
.message-system .channel-tag[data-channel="wechat"] {
    background-color: rgba(9, 187, 7, 0.2);
    color: rgba(7, 130, 5, 0.8);
}

.message-user .channel-tag[data-channel="boss"],
.message-system .channel-tag[data-channel="boss"] {
    background-color: rgba(255, 119, 0, 0.2);
    color: rgba(166, 77, 0, 0.8);
}

/* 消息元数据样式 */
.message-meta {
    margin-top: 8px;
    font-size: 11px;
    color: var(--text-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-time {
    font-style: italic;
    margin-right: auto; /* 将时间戳推到最左侧 */
}

/* 分数跟踪按钮和Actions按钮共享样式 */
.score-tracking-btn,
.actions-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 12px;
    cursor: pointer;
    padding: 3px 6px;
    margin-left: 8px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 22px;
    width: 22px;
}

.message-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    margin-top: 10px;
    color: var(--text-light);
}

.message-loading .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-light);
    animation: pulse 1.5s infinite ease-in-out;
}

.message-loading .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.message-loading .dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0.5);
        opacity: 0.5;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 按钮样式调整 */
.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .interview-container {
        flex-direction: column;
        height: auto;
    }
    
    .users-panel {
        max-width: none;
        height: 300px;
    }
}

/* 提示词模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
    display: block;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    width: 90%;
    max-width: 1200px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--secondary-color, #2c3e50);
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

/* 提示词编辑器和预览样式 */
.prompt-editor-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.prompt-editor-section {
    flex: 1;
}

.prompt-preview-section {
    flex: 1;
    border-left: 1px solid #e0e0e0;
    padding-left: 20px;
}

.prompt-editor-wrapper {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.prompt-editor {
    width: 100%;
    min-height: 300px;
    padding: 15px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
    border: none;
    overflow: auto;
    resize: none;
    box-sizing: border-box;
}

.prompt-preview {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 300px;
    padding: 15px;
    overflow-y: auto;
}

/* Markdown样式 */
.prompt-preview h1, 
.prompt-preview h2, 
.prompt-preview h3, 
.prompt-preview h4, 
.prompt-preview h5, 
.prompt-preview h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
}

.prompt-preview code {
    background-color: #eee;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', Courier, monospace;
}

.prompt-preview pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}

.prompt-preview blockquote {
    border-left: 3px solid #ddd;
    margin-left: 0;
    padding-left: 15px;
    color: #555;
}

.prompt-preview .error {
    color: var(--danger-color, #e74c3c);
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .prompt-editor-container {
        flex-direction: column;
    }
    
    .prompt-preview-section {
        border-left: none;
        padding-left: 0;
        border-top: 1px solid #e0e0e0;
        padding-top: 20px;
        margin-top: 20px;
    }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
    z-index: 2000;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
}

.notification-info {
    background-color: var(--info-color, #3498db);
}

.notification-success {
    background-color: var(--success-color, #2ecc71);
}

.notification-warning {
    background-color: var(--warning-color, #f39c12);
}

.notification-error {
    background-color: var(--danger-color, #e74c3c);
}

.notification-hide {
    animation: slideOut 0.3s ease-in;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 分数跟踪按钮样式 */
.score-tracking-btn.score-increase {
    border-color: #2ecc71;
}

.score-tracking-btn.score-increase i {
    color: #2ecc71;
}

/* 分数减少状态 */
.score-tracking-btn.score-decrease {
    border-color: #e74c3c;
}

.score-tracking-btn.score-decrease i {
    color: #e74c3c;
}

/* 历史记录高亮效果 */
@keyframes pulseHighlight {
    0% { transform: scale(1); opacity: 1; box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
    50% { transform: scale(1.1); opacity: 1; box-shadow: 0 0 0 5px rgba(52, 152, 219, 0.3); }
    100% { transform: scale(1); opacity: 1; box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

.score-tracking-btn.highlight-history {
    animation: pulseHighlight 1s ease-in-out;
}

.score-tracking-btn i {
    font-size: 11px;
}

.score-tracking-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.score-tracking-btn:active {
    transform: scale(0.95);
}

/* 分数跟踪弹窗样式 */
.score-tracking-popup {
    position: fixed;
    min-width: 280px;
    max-width: 320px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    padding: 15px;
    z-index: 1500;
    border: 1px solid var(--border-color);
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.score-tracking-close {
    position: absolute;
    top: 8px;
    right: 10px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-light);
    padding: 0;
}

.score-tracking-close:hover {
    color: var(--primary-color);
}

.score-tracking-popup h4 {
    margin: 0 0 12px 0;
    color: var(--secondary-color);
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.score-tracking-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.score-change {
    margin: 5px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.score-positive {
    color: #2ecc71;
}

.score-negative {
    color: #e74c3c;
}

.score-reasons-title {
    font-weight: 600;
    margin-top: 5px;
    font-size: 14px;
}

.score-reasons-list {
    margin: 5px 0 0 0;
    padding-left: 20px;
    font-size: 13px;
    color: var(--text-color);
}

.score-reasons-list li {
    margin-bottom: 4px;
} 

/* Actions按钮样式 */
.actions-btn i {
    font-size: 11px;
}

/* 添加高亮样式 - 当有数据时 */
.actions-btn.has-actions {
    border-color: #3498db;
}

.actions-btn .has-data {
    color: #3498db;
}

.actions-btn:hover {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.actions-btn:active {
    transform: scale(0.95);
}

/* Actions弹窗样式 */
.actions-popup {
    position: fixed;
    min-width: 300px;
    max-width: 500px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    padding: 0;
    z-index: 1500;
    border: 1px solid var(--border-color);
    animation: fadeIn 0.2s ease-out;
}

.actions-popup-header {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    color: var(--secondary-color);
    font-weight: 600;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.actions-popup-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 15px;
}

.actions-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.actions-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.actions-item-header {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--secondary-color);
}

.actions-item-type {
    margin-bottom: 5px;
}

.actions-item-params {
    margin-bottom: 3px;
}

.actions-item pre {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 0;
    font-size: 12px;
    color: #333;
} 